#!/bin/bash

# Deploy Sealify to Kubernetes
# Usage: ./deploy.sh [namespace]

set -e

NAMESPACE=${1:-sealify}
DOMAIN="sealify.tools-country-k8s-pdc-green.hcnet.vn"
IMAGE_TAG=${IMAGE_TAG:-latest}

echo "🚀 Deploying Sealify to Kubernetes cluster..."
echo "📦 Namespace: $NAMESPACE"
echo "🌐 Domain: $DOMAIN"
echo "🏷️  Image Tag: $IMAGE_TAG"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster"
    exit 1
fi

echo "✅ Connected to cluster: $(kubectl config current-context)"

# Create namespace if it doesn't exist
if ! kubectl get namespace $NAMESPACE &> /dev/null; then
    echo "📝 Creating namespace: $NAMESPACE"
    kubectl create namespace $NAMESPACE
else
    echo "✅ Namespace $NAMESPACE already exists"
fi

# Check if storage class exists
if ! kubectl get storageclass ceph-rbd-sc &> /dev/null; then
    echo "⚠️  Storage class 'ceph-rbd-sc' not found. Please ensure it exists."
    echo "   You can list available storage classes with: kubectl get storageclass"
fi

# Check if cert-manager cluster issuer exists
if ! kubectl get clusterissuer vn-ingress &> /dev/null; then
    echo "⚠️  ClusterIssuer 'vn-ingress' not found. Please ensure cert-manager is installed and configured."
    echo "   You can list cluster issuers with: kubectl get clusterissuer"
fi

# Apply manifests
echo "📋 Applying Kubernetes manifests..."
kubectl apply -f k8s-manifests.yaml

# Wait for deployment to be ready
echo "⏳ Waiting for deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/sealify -n $NAMESPACE

# Get deployment status
echo "📊 Deployment Status:"
kubectl get pods -n $NAMESPACE -l app=sealify
echo ""
kubectl get svc -n $NAMESPACE -l app=sealify
echo ""
kubectl get ingress -n $NAMESPACE

# Get the application URL
echo ""
echo "🎉 Deployment completed successfully!"
echo "🌐 Application URL: https://$DOMAIN"
echo "📱 Health check: https://$DOMAIN/api/health"

# Show logs from one pod
echo ""
echo "📝 Recent logs from one pod:"
kubectl logs -n $NAMESPACE -l app=sealify --tail=10

echo ""
echo "🔍 To view logs: kubectl logs -f -n $NAMESPACE -l app=sealify"
echo "🔧 To debug: kubectl describe deployment sealify -n $NAMESPACE"
