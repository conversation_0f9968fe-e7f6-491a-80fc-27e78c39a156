{"level": "error", "message": "Error converting secret: Assignment to constant variable.", "service": "sealed-secret-manager", "stack": "TypeError: Assignment to constant variable.\n    at /Users/<USER>/HC/Platform-Tools/chore-tasks/server.js:447:24\n    at Layer.handle [as handle_request] (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/layer.js:95:5)", "timestamp": "2025-07-30T14:55:09.880Z"}