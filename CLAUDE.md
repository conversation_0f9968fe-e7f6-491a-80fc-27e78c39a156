# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Sealify is a Node.js web application for managing Kubernetes Sealed Secrets. It provides a web interface for converting regular Kubernetes secrets to sealed secrets using the kubeseal tool, managing cluster certificates, and Base64 encoding/decoding utilities.

## Key Commands

### Development
- `npm install` - Install dependencies
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm run install-kubeseal` - Install kubeseal binary (automated script)

### Docker
- `docker build -t sealify .` - Build Docker image
- Multi-stage build supports both development and production targets

### Kubernetes
- `kubectl apply -f k8s-manifests.yaml` - Deploy to Kubernetes
- `kubectl port-forward -n sealify svc/sealify-service 3000:80` - Port forward for testing

## Architecture

### Core Components
1. **server.js**: Main Express.js server with all API routes and middleware
2. **database.js**: Simple file-based database abstraction for cluster management
3. **Frontend**: Vanilla HTML/CSS/JS (index.html, script.js, styles.css)
4. **Kubeseal Integration**: Spawns kubeseal processes for secret encryption

### API Structure
- **Health**: `/api/health` - Server status and kubeseal availability
- **Clusters**: CRUD operations for cluster management (`/api/clusters`)
- **Conversion**: `/api/convert` - Convert secrets to sealed secrets
- **Base64**: `/api/base64/encode` and `/api/base64/decode` utilities

### Data Storage
- **File-based storage** using JSON files in `./data/`
- **Certificates** stored in `./certs/` with UUID naming
- **Logs** written to `./logs/` using Winston
- **Temporary files** in `./tmp/` for kubeseal operations

### Key Dependencies
- **express**: Web server framework
- **multer**: File upload handling for certificates
- **js-yaml**: YAML parsing and generation
- **winston**: Logging
- **fs-extra**: Enhanced filesystem operations
- **uuid**: Unique identifier generation

## Development Notes

### Kubeseal Integration
- The app requires the kubeseal binary to be installed for full functionality
- kubeseal is spawned as a child process with certificate files for encryption
- Temporary YAML files are created and cleaned up during conversion

### Security Features
- File upload validation and size limits (5MB)
- PEM certificate format validation
- Input sanitization for YAML content
- No secret storage - secrets are only processed, never persisted

### Error Handling
- Comprehensive error logging with Winston
- Graceful handling of missing kubeseal binary
- File cleanup on operation failures
- Proper HTTP status codes for all API responses

### Frontend Architecture
- Single-page application with vanilla JavaScript
- Tab-based navigation between sealed secrets, Base64 tools, and admin
- Real-time feedback for operations
- Copy-to-clipboard functionality

## File Structure
```
├── server.js              # Main server with all routes and logic
├── database.js             # Database abstraction layer
├── index.html              # Frontend HTML template
├── script.js               # Frontend JavaScript
├── styles.css              # CSS styles
├── package.json            # Dependencies and scripts
├── Dockerfile              # Multi-stage Docker build
├── k8s-manifests.yaml      # Kubernetes deployment resources
├── scripts/
│   └── install-kubeseal.js # Automated kubeseal installation
├── data/                   # JSON data files (persistent)
├── certs/                  # Certificate files (persistent)
├── logs/                   # Application logs
└── tmp/                    # Temporary files (cleaned up)
```

## Testing
- No formal test suite is currently configured
- Testing relies on manual verification and health checks
- The `/api/health` endpoint provides service status