# Sealify Kubernetes Deployment

## Overview
This document describes how to deploy Sealify to your Kubernetes cluster.

## Prerequisites

- Kubernetes cluster with:
  - Storage class: `ceph-rbd-sc`
  - Cert-manager installed with cluster issuer: `vn-ingress`
  - NGINX Ingress Controller
- kubectl configured to access your cluster
- Docker (for building images)

## Domain
The application will be deployed at: `sealify.tools-country-k8s-pdc-green.hcnet.vn`

## Quick Deploy

### 1. Build the Docker image
```bash
# Build and optionally push to your registry
./build.sh latest your-registry.com

# Or build for local use
docker build -t sealify:latest --target production .
```

### 2. Update image in manifests (if needed)
Edit `k8s-manifests.yaml` and update the image name:
```yaml
containers:
- name: sealify
  image: your-registry.com/sealify:latest  # Update this line
```

### 3. Deploy to Kubernetes
```bash
# Deploy with default settings
./deploy.sh

# Or deploy to custom namespace
./deploy.sh my-namespace
```

## Manual Deployment

If you prefer to deploy manually:

```bash
# Apply all manifests
kubectl apply -f k8s-manifests.yaml

# Check deployment status
kubectl get pods -n sealify
kubectl get svc -n sealify
kubectl get ingress -n sealify
```

## Configuration

### Storage
- **Storage Class**: `ceph-rbd-sc`
- **Storage Size**: 2Gi
- **Access Mode**: ReadWriteOnce

### SSL/TLS
- **Domain**: `sealify.tools-country-k8s-pdc-green.hcnet.vn`
- **Cert-Manager Issuer**: `vn-ingress`
- **TLS Secret**: `sealify-tls` (auto-generated)

### Resources
- **CPU Request**: 200m
- **Memory Request**: 256Mi
- **CPU Limit**: 1000m
- **Memory Limit**: 1Gi
- **Replicas**: 2 (with rolling update strategy)

## Health Checks

The application provides a health endpoint at `/api/health` for:
- Liveness probe (every 10s after 30s)
- Readiness probe (every 5s after 5s)

## Monitoring

### View logs
```bash
# Follow logs from all pods
kubectl logs -f -n sealify -l app=sealify

# View logs from specific pod
kubectl logs -n sealify deployment/sealify
```

### Check status
```bash
# Pod status
kubectl get pods -n sealify -l app=sealify

# Service status
kubectl get svc -n sealify

# Ingress status
kubectl get ingress -n sealify

# Describe deployment
kubectl describe deployment sealify -n sealify
```

## Troubleshooting

### Common Issues

1. **Storage class not found**
   ```bash
   kubectl get storageclass
   # Make sure 'ceph-rbd-sc' exists
   ```

2. **Cert-manager issues**
   ```bash
   kubectl get clusterissuer
   # Make sure 'vn-ingress' exists
   
   kubectl get certificate -n sealify
   # Check certificate status
   ```

3. **Image pull issues**
   - Ensure the image exists and is accessible
   - Check image pull secrets if using private registry

4. **Pod not starting**
   ```bash
   kubectl describe pod -n sealify -l app=sealify
   kubectl logs -n sealify -l app=sealify
   ```

### Scale deployment
```bash
# Scale to 3 replicas
kubectl scale deployment sealify --replicas=3 -n sealify
```

### Update deployment
```bash
# Update image
kubectl set image deployment/sealify sealify=sealify:new-tag -n sealify

# Or apply updated manifests
kubectl apply -f k8s-manifests.yaml
```

## Security

- Non-root user (UID 1001)
- Read-only root filesystem (optional)
- Dropped capabilities
- Security context configured
- HTTPS enforced with SSL redirect

## Cleanup

To remove the deployment:
```bash
kubectl delete -f k8s-manifests.yaml
# Or
kubectl delete namespace sealify
```
