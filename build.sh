#!/bin/bash

# Build and push Sealify Docker image
# Usage: ./build.sh [tag] [registry]

set -e

TAG=${1:-latest}
REGISTRY=${2:-"your-registry.com"}
IMAGE_NAME="sealify"
FULL_IMAGE="$REGISTRY/$IMAGE_NAME:$TAG"

echo "🏗️  Building Sealify Docker image..."
echo "🏷️  Tag: $TAG"
echo "📦 Full image name: $FULL_IMAGE"

# Build the image
echo "🔨 Building Docker image..."
docker build -t $IMAGE_NAME:$TAG -t $FULL_IMAGE --target production .

echo "✅ Image built successfully!"

# Show image size
echo "📏 Image size:"
docker images $IMAGE_NAME:$TAG --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Ask if user wants to push
read -p "🚀 Do you want to push the image to registry? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📤 Pushing image to registry..."
    docker push $FULL_IMAGE
    echo "✅ Image pushed successfully!"
    
    echo ""
    echo "🎯 To deploy with this image:"
    echo "   export IMAGE_TAG=$TAG"
    echo "   ./deploy.sh"
else
    echo "⏭️  Skipping push to registry"
    echo ""
    echo "🎯 To deploy locally built image:"
    echo "   # Update k8s-manifests.yaml to use: $IMAGE_NAME:$TAG"
    echo "   # Then run: ./deploy.sh"
fi

echo ""
echo "📝 Available commands:"
echo "   - View image: docker images $IMAGE_NAME"
echo "   - Run locally: docker run -p 3000:3000 $IMAGE_NAME:$TAG"
echo "   - Push later: docker push $FULL_IMAGE"
